import { ConfigProvider, theme as antdTheme } from 'antd'
import { Modal } from 'antd'
import { createRoot } from 'react-dom/client'

import { Clear } from '@/components'
import customTheme from '@/styles/theme/antd'

// 基本使用
// const result = await Confirm.confirm({
//   content: '确定要执行此操作吗？'
// });

// 自定义配置
// const result = await Confirm.confirm({
//   title: '删除确认',
//   content: '此操作不可恢复，是否继续？',
//   okText: '删除',
//   cancelText: '取消',
//   width: 500
// });

// 在异步函数中使用
// async function handleDelete() {
//   const confirmed = await Confirm.confirm({
//     content: '确定要删除吗？'
//   });

//   if (confirmed) {
//     await deleteItem();
//   }
// }

interface ConfirmOptions {
  title?: string
  content?: React.ReactNode
  okText?: string
  cancelText?: string
  width?: number
}

let currentRoot: ReturnType<typeof createRoot> | null = null

export const Confirm = {
  confirm: (options: ConfirmOptions = {}): Promise<boolean> => {
    return new Promise((resolve) => {
      // 创建容器
      const container = document.createElement('div')
      container.className = 'confirm-modal-container'
      document.body.appendChild(container)
      currentRoot = createRoot(container)

      // 获取当前的 antd ConfigProvider
      const existingConfigProvider = document.querySelector('.ant-app')
      const configProviderTheme = existingConfigProvider?.getAttribute('data-theme') || 'default'

      // 清理函数
      const destroy = () => {
        if (currentRoot) {
          currentRoot.unmount()
          container.remove()
          currentRoot = null
        }
      }

      // 处理确认和取消
      const handleConfirm = () => {
        destroy()
        resolve(true)
      }

      const handleCancel = () => {
        destroy()
        resolve(false)
      }

      // 渲染 Modal，包裹在 ConfigProvider 中以继承主题
      currentRoot.render(
        <ConfigProvider
          theme={{
            ...customTheme,
            algorithm:
              configProviderTheme === 'dark' ? antdTheme.darkAlgorithm : antdTheme.defaultAlgorithm,
          }}>
          <div className={existingConfigProvider?.className}>
            <Modal
              className="custom_modal"
              title={options.title || '确认'}
              closeIcon={<Clear fill={isHovered ? '#da291c' : 'black'} width={24} height={24} />}
              open={true}
              onCancel={handleCancel}
              onOk={handleConfirm}
              okText={options.okText || '确定'}
              cancelText={options.cancelText || '取消'}
              width={options.width || 400}
              centered
              maskClosable={false}>
              {options.content}
            </Modal>
          </div>
        </ConfigProvider>,
      )
    })
  },
}
